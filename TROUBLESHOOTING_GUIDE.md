# Scalping EA Troubleshooting Guide

## 🚨 Critical Bugs Fixed

### **Bug #1: Incorrect Spread Calculation**
**Problem**: EA was rejecting all trades due to wrong spread calculation
**Fixed**: Corrected spread calculation from points to pips
```mql5
// OLD (WRONG):
if(spread > MaxSpread * 10) // Wrong conversion
   Print("Spread too high: ", spread/_Point, " pips"); // Double division!

// NEW (FIXED):
double spreadPips = spread / 10; // Convert points to pips
if(spreadPips > MaxSpread)
   Print("Spread too high: ", DoubleToString(spreadPips, 1), " pips");
```

### **Bug #2: Position Count Logic Error**
**Problem**: EA checked ALL positions, not just its own
**Fixed**: Added function to count only EA's positions with correct Magic Number
```mql5
// OLD (WRONG):
if(PositionsTotal() > 0) // Counts ALL positions!

// NEW (FIXED):
if(CountEAPositions() > 0) // Only counts EA's positions
```

### **Bug #3: Session Filter Logic**
**Problem**: If both sessions disabled, EA never traded
**Fixed**: Allow trading anytime if both sessions disabled
```mql5
// NEW (FIXED):
if(!UseLondonSession && !UseNewYorkSession)
   return true; // Allow trading anytime
```

## 🔍 Debugging Steps

### **Step 1: Enable Logging**
Set `EnableLogging = true` in EA inputs to see detailed logs.

### **Step 2: Check Expert Tab Messages**
Look for these key messages in MT5 Expert tab:

#### **Initialization Messages:**
- ✅ `"ScalpingEA initialized successfully on EURUSD M15"`
- ❌ `"Error creating EMA indicators"` - Check symbol/timeframe

#### **Trading Condition Messages:**
- ✅ `"New bar detected at [time]"`
- ❌ `"Trading not allowed"` - Check conditions below
- ❌ `"Failed to update indicators"` - Data issue

#### **Spread Messages:**
- ✅ Normal operation (no spread messages)
- ❌ `"Spread too high: X.X pips (Max: 3.0)"` - Increase MaxSpread

#### **Session Messages:**
- ✅ Normal operation (no session messages)
- ❌ `"Outside trading session. Current GMT hour: XX"` - Check session settings

#### **Signal Detection Messages:**
- ✅ `"Signal Check - Bullish Engulfing: true | Buy Trend: true"`
- ✅ `"BUY SIGNAL DETECTED - Opening trade..."`
- ❌ All conditions false - No patterns/trend found

#### **Trade Opening Messages:**
- ✅ `"BUY trade opened: Ticket=XXX Entry=X.XXXXX SL=X.XXXXX"`
- ❌ `"Failed to open BUY trade. Error: XXXX"` - Check account/margin

### **Step 3: Common Issues & Solutions**

#### **Issue: "Trading not allowed"**
**Causes & Solutions:**
1. **High Spread**: Increase `MaxSpread` parameter
2. **Wrong Session**: Check GMT time vs session hours
3. **Daily Limit**: Check if `MaxTradesPerDay` reached
4. **Weekend**: EA doesn't trade on weekends

#### **Issue: "No signals detected"**
**Causes & Solutions:**
1. **No Engulfing Patterns**: Wait for market volatility
2. **Wrong Trend**: Price not aligned with EMAs
3. **Timeframe**: Ensure chart is M15
4. **Data Issues**: Restart EA or check data feed

#### **Issue: "Failed to open trade"**
**Causes & Solutions:**
1. **Insufficient Margin**: Reduce lot size or increase balance
2. **Market Closed**: Check trading hours
3. **Invalid Stops**: SL/TP too close to market price
4. **Connection Issues**: Check internet/broker connection

## 📊 Parameter Optimization for Different Symbols

### **For EURUSD (Default):**
```
MaxSpread = 3.0
BreakEvenPips = 10
TrailStartPips = 15
TrailStepPips = 7
RiskPercent = 2.0
```

### **For XAUUSD (Gold):**
```
MaxSpread = 8.0          // Higher spreads
BreakEvenPips = 20       // More volatile
TrailStartPips = 30      // Wider movements
TrailStepPips = 15       // Avoid premature exits
RiskPercent = 1.0        // Reduce risk
```

### **For GBPUSD:**
```
MaxSpread = 4.0          // Slightly higher spreads
BreakEvenPips = 12       // More volatile than EUR
TrailStartPips = 18      
TrailStepPips = 8
RiskPercent = 1.5        // Reduce risk slightly
```

## 🕐 Session Time Troubleshooting

### **Check Current GMT Time:**
```mql5
MqlDateTime dt;
TimeToStruct(TimeGMT(), dt);
Print("Current GMT hour: ", dt.hour);
```

### **Session Hours (GMT):**
- **London**: 8:00 - 17:00 GMT
- **New York**: 13:00 - 22:00 GMT  
- **Overlap**: 13:00 - 17:00 GMT (Best time)

### **Broker Time vs GMT:**
- Most brokers use GMT+2 (summer) or GMT+3 (winter)
- Adjust session hours accordingly
- Or disable session filter: Set both `UseLondonSession` and `UseNewYorkSession` to `false`

## 🔧 Manual Testing Steps

### **Test 1: Basic Functionality**
1. Attach EA to EURUSD M15 chart
2. Set `EnableLogging = true`
3. Wait for new bar (max 15 minutes)
4. Check Expert tab for "New bar detected" message

### **Test 2: Spread Check**
1. Check current spread: `(Ask - Bid) / Point / 10`
2. Compare with `MaxSpread` setting
3. If spread > MaxSpread, increase the parameter

### **Test 3: Session Check**
1. Note current GMT time
2. Check if within London (8-17) or NY (13-22) hours
3. If outside, either wait or disable session filters

### **Test 4: Pattern Detection**
1. Look for engulfing patterns on chart manually
2. Check if price is above/below both EMAs
3. Wait for confluence of pattern + trend

## 📈 Performance Monitoring

### **Daily Checks:**
- Number of trades opened
- Win/loss ratio
- Maximum drawdown
- Spread conditions

### **Weekly Reviews:**
- Adjust parameters based on market conditions
- Review trade logs for patterns
- Check if session times need adjustment

### **Monthly Optimization:**
- Backtest with current parameters
- Compare with different settings
- Adjust risk based on performance

## 🆘 Emergency Stops

### **If EA Behaving Erratically:**
1. **Disable AutoTrading** immediately
2. **Close all positions** manually if needed
3. **Check logs** for error messages
4. **Restart EA** with conservative settings

### **Safe Mode Settings:**
```
RiskPercent = 0.5        // Minimal risk
MaxTradesPerDay = 1      // One trade only
MaxSpread = 1.5          // Very tight spread
FixedLotSize = 0.01      // Micro lots
```

Remember: Always test on demo account first and never risk more than you can afford to lose!
