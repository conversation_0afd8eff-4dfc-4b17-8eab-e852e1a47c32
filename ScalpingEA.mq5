//+------------------------------------------------------------------+
//|                                                   ScalpingEA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Scalping EA based on Engulfing Patterns and EMA Trend Confirmation"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Create trade objects
CTrade trade;
CPositionInfo position;
COrderInfo order;

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== STRATEGY SETTINGS ==="
input int      FastEMA_Period = 9;           // Fast EMA Period
input int      SlowEMA_Period = 50;          // Slow EMA Period
input double   RiskPercent = 2.0;            // Risk per trade (% of balance)
input double   TPMultiplier = 1.5;           // Take Profit multiplier (x SL distance)

input group "=== TRAILING STOP SETTINGS ==="
input int      BreakEvenPips = 10;           // Move to BE after X pips profit
input int      TrailStartPips = 15;          // Start trailing after X pips profit
input int      TrailStepPips = 7;            // Trail step in pips

input group "=== RISK MANAGEMENT ==="
input double   MaxSpread = 3.0;              // Maximum spread allowed (pips)
input int      MaxTradesPerDay = 5;          // Maximum trades per day
input double   FixedLotSize = 0.0;           // Fixed lot size (0 = auto calculation)

input group "=== SESSION FILTER ==="
input bool     UseLondonSession = true;      // Trade during London session
input bool     UseNewYorkSession = true;     // Trade during New York session
input int      LondonStartHour = 8;          // London session start hour (GMT)
input int      LondonEndHour = 17;           // London session end hour (GMT)
input int      NewYorkStartHour = 13;        // New York session start hour (GMT)
input int      NewYorkEndHour = 22;          // New York session end hour (GMT)

input group "=== GENERAL SETTINGS ==="
input int      MagicNumber = 123456;         // Magic number for trades
input bool     EnableLogging = true;         // Enable detailed logging
input string   TradeComment = "ScalpingEA";  // Trade comment

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
int fastEMA_handle, slowEMA_handle;
double fastEMA[], slowEMA[];
datetime lastBarTime = 0;
int tradesCountToday = 0;
datetime lastTradeDate = 0;

//--- Trade tracking variables
struct TradeInfo
{
   ulong ticket;
   double entryPrice;
   double initialSL;
   double initialTP;
   bool movedToBE;
   bool trailingActive;
   datetime entryTime;
};

TradeInfo currentTrades[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Set magic number for trade object
   trade.SetExpertMagicNumber(MagicNumber);
   
   //--- Create EMA indicators
   fastEMA_handle = iMA(_Symbol, PERIOD_M15, FastEMA_Period, 0, MODE_EMA, PRICE_CLOSE);
   slowEMA_handle = iMA(_Symbol, PERIOD_M15, SlowEMA_Period, 0, MODE_EMA, PRICE_CLOSE);
   
   if(fastEMA_handle == INVALID_HANDLE || slowEMA_handle == INVALID_HANDLE)
   {
      Print("Error creating EMA indicators");
      return INIT_FAILED;
   }
   
   //--- Set array as series
   ArraySetAsSeries(fastEMA, true);
   ArraySetAsSeries(slowEMA, true);
   
   //--- Initialize trade tracking array
   ArrayResize(currentTrades, 0);
   
   //--- Reset daily trade counter
   ResetDailyTradeCount();

   //--- Set timer for periodic tasks (every 60 seconds)
   EventSetTimer(60);

   if(EnableLogging)
      Print("ScalpingEA initialized successfully on ", _Symbol, " M15");

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Kill timer
   EventKillTimer();

   //--- Release indicator handles
   if(fastEMA_handle != INVALID_HANDLE)
      IndicatorRelease(fastEMA_handle);
   if(slowEMA_handle != INVALID_HANDLE)
      IndicatorRelease(slowEMA_handle);

   if(EnableLogging)
      Print("ScalpingEA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check if new bar formed
   if(!IsNewBar())
      return;
   
   //--- Update indicators
   if(!UpdateIndicators())
      return;
   
   //--- Check trading conditions
   if(!IsTradingAllowed())
      return;
   
   //--- Manage existing positions
   ManagePositions();
   
   //--- Look for new trading opportunities
   CheckForTradingSignals();
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
   datetime currentBarTime = iTime(_Symbol, PERIOD_M15, 0);
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Update indicator values                                          |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
   //--- Copy EMA values
   if(CopyBuffer(fastEMA_handle, 0, 0, 3, fastEMA) < 3)
      return false;
   if(CopyBuffer(slowEMA_handle, 0, 0, 3, slowEMA) < 3)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool IsTradingAllowed()
{
   //--- Check spread
   double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
   if(spread > MaxSpread * 10) // Convert pips to points
   {
      if(EnableLogging)
         Print("Spread too high: ", spread/_Point, " pips");
      return false;
   }
   
   //--- Check daily trade limit
   if(tradesCountToday >= MaxTradesPerDay)
   {
      if(EnableLogging)
         Print("Daily trade limit reached: ", tradesCountToday);
      return false;
   }
   
   //--- Check trading session
   if(!IsInTradingSession())
   {
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Check if current time is in allowed trading session             |
//+------------------------------------------------------------------+
bool IsInTradingSession()
{
   MqlDateTime dt;
   TimeToStruct(TimeGMT(), dt);
   int currentHour = dt.hour;
   
   bool inLondon = false;
   bool inNewYork = false;
   
   if(UseLondonSession)
   {
      if(LondonStartHour <= LondonEndHour)
         inLondon = (currentHour >= LondonStartHour && currentHour < LondonEndHour);
      else
         inLondon = (currentHour >= LondonStartHour || currentHour < LondonEndHour);
   }
   
   if(UseNewYorkSession)
   {
      if(NewYorkStartHour <= NewYorkEndHour)
         inNewYork = (currentHour >= NewYorkStartHour && currentHour < NewYorkEndHour);
      else
         inNewYork = (currentHour >= NewYorkStartHour || currentHour < NewYorkEndHour);
   }
   
   return (inLondon || inNewYork);
}

//+------------------------------------------------------------------+
//| Reset daily trade count                                          |
//+------------------------------------------------------------------+
void ResetDailyTradeCount()
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   datetime currentDate = StringToTime(StringFormat("%04d.%02d.%02d", dt.year, dt.mon, dt.day));

   if(currentDate != lastTradeDate)
   {
      tradesCountToday = 0;
      lastTradeDate = currentDate;
      if(EnableLogging)
         Print("Daily trade count reset for new day: ", TimeToString(currentDate, TIME_DATE));
   }
}

//+------------------------------------------------------------------+
//| Detect bullish engulfing pattern                                 |
//+------------------------------------------------------------------+
bool IsBullishEngulfing()
{
   //--- Get OHLC data for last 2 candles
   double open1 = iOpen(_Symbol, PERIOD_M15, 1);   // Previous candle open
   double close1 = iClose(_Symbol, PERIOD_M15, 1); // Previous candle close
   double high1 = iHigh(_Symbol, PERIOD_M15, 1);   // Previous candle high
   double low1 = iLow(_Symbol, PERIOD_M15, 1);     // Previous candle low

   double open2 = iOpen(_Symbol, PERIOD_M15, 2);   // 2 candles ago open
   double close2 = iClose(_Symbol, PERIOD_M15, 2); // 2 candles ago close
   double high2 = iHigh(_Symbol, PERIOD_M15, 2);   // 2 candles ago high
   double low2 = iLow(_Symbol, PERIOD_M15, 2);     // 2 candles ago low

   //--- Check for bullish engulfing pattern
   // 1. Previous candle (candle 2) should be bearish
   bool prevCandleBearish = (close2 < open2);

   // 2. Current candle (candle 1) should be bullish
   bool currentCandleBullish = (close1 > open1);

   // 3. Current candle should engulf previous candle
   bool engulfs = (open1 < close2) && (close1 > open2);

   // 4. Current candle body should be larger than previous candle body
   double currentBody = MathAbs(close1 - open1);
   double prevBody = MathAbs(close2 - open2);
   bool largerBody = (currentBody > prevBody);

   return (prevCandleBearish && currentCandleBullish && engulfs && largerBody);
}

//+------------------------------------------------------------------+
//| Detect bearish engulfing pattern                                 |
//+------------------------------------------------------------------+
bool IsBearishEngulfing()
{
   //--- Get OHLC data for last 2 candles
   double open1 = iOpen(_Symbol, PERIOD_M15, 1);   // Previous candle open
   double close1 = iClose(_Symbol, PERIOD_M15, 1); // Previous candle close
   double high1 = iHigh(_Symbol, PERIOD_M15, 1);   // Previous candle high
   double low1 = iLow(_Symbol, PERIOD_M15, 1);     // Previous candle low

   double open2 = iOpen(_Symbol, PERIOD_M15, 2);   // 2 candles ago open
   double close2 = iClose(_Symbol, PERIOD_M15, 2); // 2 candles ago close
   double high2 = iHigh(_Symbol, PERIOD_M15, 2);   // 2 candles ago high
   double low2 = iLow(_Symbol, PERIOD_M15, 2);     // 2 candles ago low

   //--- Check for bearish engulfing pattern
   // 1. Previous candle (candle 2) should be bullish
   bool prevCandleBullish = (close2 > open2);

   // 2. Current candle (candle 1) should be bearish
   bool currentCandleBearish = (close1 < open1);

   // 3. Current candle should engulf previous candle
   bool engulfs = (open1 > close2) && (close1 < open2);

   // 4. Current candle body should be larger than previous candle body
   double currentBody = MathAbs(close1 - open1);
   double prevBody = MathAbs(close2 - open2);
   bool largerBody = (currentBody > prevBody);

   return (prevCandleBullish && currentCandleBearish && engulfs && largerBody);
}

//+------------------------------------------------------------------+
//| Check EMA trend for BUY signal                                   |
//+------------------------------------------------------------------+
bool IsBuyTrendConfirmed()
{
   //--- Check if price is above both EMAs
   double currentPrice = iClose(_Symbol, PERIOD_M15, 1);

   // Price should be above both fast and slow EMA
   bool aboveFastEMA = (currentPrice > fastEMA[1]);
   bool aboveSlowEMA = (currentPrice > slowEMA[1]);

   // Fast EMA should be above slow EMA (uptrend)
   bool fastAboveSlow = (fastEMA[1] > slowEMA[1]);

   return (aboveFastEMA && aboveSlowEMA && fastAboveSlow);
}

//+------------------------------------------------------------------+
//| Check EMA trend for SELL signal                                  |
//+------------------------------------------------------------------+
bool IsSellTrendConfirmed()
{
   //--- Check if price is below both EMAs
   double currentPrice = iClose(_Symbol, PERIOD_M15, 1);

   // Price should be below both fast and slow EMA
   bool belowFastEMA = (currentPrice < fastEMA[1]);
   bool belowSlowEMA = (currentPrice < slowEMA[1]);

   // Fast EMA should be below slow EMA (downtrend)
   bool fastBelowSlow = (fastEMA[1] < slowEMA[1]);

   return (belowFastEMA && belowSlowEMA && fastBelowSlow);
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckForTradingSignals()
{
   //--- Don't open new trades if we already have one
   if(PositionsTotal() > 0)
      return;

   //--- Check for BUY signal
   if(IsBullishEngulfing() && IsBuyTrendConfirmed())
   {
      OpenBuyTrade();
   }
   //--- Check for SELL signal
   else if(IsBearishEngulfing() && IsSellTrendConfirmed())
   {
      OpenSellTrade();
   }
}

//+------------------------------------------------------------------+
//| Open BUY trade                                                   |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
   //--- Get signal candle data
   double signalHigh = iHigh(_Symbol, PERIOD_M15, 1);
   double signalLow = iLow(_Symbol, PERIOD_M15, 1);
   double signalClose = iClose(_Symbol, PERIOD_M15, 1);

   //--- Calculate entry price (current ask price)
   double entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

   //--- Calculate stop loss (low of signal candle)
   double stopLoss = signalLow;

   //--- Calculate SL distance in points
   double slDistance = entryPrice - stopLoss;

   //--- Calculate take profit
   double takeProfit = entryPrice + (slDistance * TPMultiplier);

   //--- Calculate lot size
   double lotSize = CalculateLotSize(slDistance);

   //--- Normalize prices
   stopLoss = NormalizeDouble(stopLoss, _Digits);
   takeProfit = NormalizeDouble(takeProfit, _Digits);

   //--- Open trade
   if(trade.Buy(lotSize, _Symbol, entryPrice, stopLoss, takeProfit, TradeComment))
   {
      //--- Store trade information
      TradeInfo newTrade;
      newTrade.ticket = trade.ResultOrder();
      newTrade.entryPrice = entryPrice;
      newTrade.initialSL = stopLoss;
      newTrade.initialTP = takeProfit;
      newTrade.movedToBE = false;
      newTrade.trailingActive = false;
      newTrade.entryTime = TimeCurrent();

      //--- Add to trades array
      int size = ArraySize(currentTrades);
      ArrayResize(currentTrades, size + 1);
      currentTrades[size] = newTrade;

      //--- Increment daily trade count
      tradesCountToday++;

      if(EnableLogging)
      {
         Print("BUY trade opened: Ticket=", newTrade.ticket,
               " Entry=", entryPrice, " SL=", stopLoss, " TP=", takeProfit,
               " Lot=", lotSize, " SL Distance=", slDistance/_Point, " pips");
      }
   }
   else
   {
      if(EnableLogging)
         Print("Failed to open BUY trade. Error: ", trade.ResultRetcode());
   }
}

//+------------------------------------------------------------------+
//| Open SELL trade                                                  |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
   //--- Get signal candle data
   double signalHigh = iHigh(_Symbol, PERIOD_M15, 1);
   double signalLow = iLow(_Symbol, PERIOD_M15, 1);
   double signalClose = iClose(_Symbol, PERIOD_M15, 1);

   //--- Calculate entry price (current bid price)
   double entryPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);

   //--- Calculate stop loss (high of signal candle)
   double stopLoss = signalHigh;

   //--- Calculate SL distance in points
   double slDistance = stopLoss - entryPrice;

   //--- Calculate take profit
   double takeProfit = entryPrice - (slDistance * TPMultiplier);

   //--- Calculate lot size
   double lotSize = CalculateLotSize(slDistance);

   //--- Normalize prices
   stopLoss = NormalizeDouble(stopLoss, _Digits);
   takeProfit = NormalizeDouble(takeProfit, _Digits);

   //--- Open trade
   if(trade.Sell(lotSize, _Symbol, entryPrice, stopLoss, takeProfit, TradeComment))
   {
      //--- Store trade information
      TradeInfo newTrade;
      newTrade.ticket = trade.ResultOrder();
      newTrade.entryPrice = entryPrice;
      newTrade.initialSL = stopLoss;
      newTrade.initialTP = takeProfit;
      newTrade.movedToBE = false;
      newTrade.trailingActive = false;
      newTrade.entryTime = TimeCurrent();

      //--- Add to trades array
      int size = ArraySize(currentTrades);
      ArrayResize(currentTrades, size + 1);
      currentTrades[size] = newTrade;

      //--- Increment daily trade count
      tradesCountToday++;

      if(EnableLogging)
      {
         Print("SELL trade opened: Ticket=", newTrade.ticket,
               " Entry=", entryPrice, " SL=", stopLoss, " TP=", takeProfit,
               " Lot=", lotSize, " SL Distance=", slDistance/_Point, " pips");
      }
   }
   else
   {
      if(EnableLogging)
         Print("Failed to open SELL trade. Error: ", trade.ResultRetcode());
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                      |
//+------------------------------------------------------------------+
double CalculateLotSize(double slDistance)
{
   //--- Use fixed lot size if specified
   if(FixedLotSize > 0)
      return FixedLotSize;

   //--- Calculate lot size based on risk percentage
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * RiskPercent / 100.0;

   //--- Convert SL distance to money
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   double slDistanceMoney = (slDistance / tickSize) * tickValue;

   //--- Calculate lot size
   double lotSize = riskAmount / slDistanceMoney;

   //--- Get symbol lot constraints
   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   //--- Normalize lot size
   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   return lotSize;
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManagePositions()
{
   //--- Loop through all tracked trades
   for(int i = ArraySize(currentTrades) - 1; i >= 0; i--)
   {
      //--- Check if position still exists
      if(!PositionSelectByTicket(currentTrades[i].ticket))
      {
         //--- Position closed, remove from tracking
         if(EnableLogging)
         {
            Print("Position closed: Ticket=", currentTrades[i].ticket,
                  " Entry=", currentTrades[i].entryPrice,
                  " Duration=", (TimeCurrent() - currentTrades[i].entryTime)/60, " minutes");
         }

         //--- Remove from array
         for(int j = i; j < ArraySize(currentTrades) - 1; j++)
            currentTrades[j] = currentTrades[j + 1];
         ArrayResize(currentTrades, ArraySize(currentTrades) - 1);
         continue;
      }

      //--- Position exists, manage trailing stop
      ManageTrailingStop(currentTrades[i]);
   }
}

//+------------------------------------------------------------------+
//| Manage trailing stop for a position                             |
//+------------------------------------------------------------------+
void ManageTrailingStop(TradeInfo &tradeInfo)
{
   //--- Get current position info
   if(!PositionSelectByTicket(tradeInfo.ticket))
      return;

   double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
   double currentSL = PositionGetDouble(POSITION_SL);
   double entryPrice = tradeInfo.entryPrice;
   ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

   //--- Calculate profit in pips
   double profitPips = 0;
   if(posType == POSITION_TYPE_BUY)
      profitPips = (currentPrice - entryPrice) / _Point;
   else
      profitPips = (entryPrice - currentPrice) / _Point;

   //--- Move to break even if profit >= BreakEvenPips
   if(!tradeInfo.movedToBE && profitPips >= BreakEvenPips * 10) // Convert pips to points
   {
      double newSL = NormalizeDouble(entryPrice, _Digits);

      if(trade.PositionModify(tradeInfo.ticket, newSL, PositionGetDouble(POSITION_TP)))
      {
         tradeInfo.movedToBE = true;
         if(EnableLogging)
            Print("Moved to Break Even: Ticket=", tradeInfo.ticket, " New SL=", newSL, " Profit=", profitPips/10, " pips");
      }
   }

   //--- Start trailing if profit >= TrailStartPips
   if(profitPips >= TrailStartPips * 10) // Convert pips to points
   {
      tradeInfo.trailingActive = true;

      double newSL = 0;
      bool shouldModify = false;

      if(posType == POSITION_TYPE_BUY)
      {
         //--- For BUY positions, trail SL up
         newSL = currentPrice - (TrailStepPips * 10 * _Point);
         newSL = NormalizeDouble(newSL, _Digits);

         //--- Only modify if new SL is higher than current SL
         if(newSL > currentSL)
            shouldModify = true;
      }
      else
      {
         //--- For SELL positions, trail SL down
         newSL = currentPrice + (TrailStepPips * 10 * _Point);
         newSL = NormalizeDouble(newSL, _Digits);

         //--- Only modify if new SL is lower than current SL
         if(newSL < currentSL)
            shouldModify = true;
      }

      //--- Modify stop loss if needed
      if(shouldModify)
      {
         if(trade.PositionModify(tradeInfo.ticket, newSL, PositionGetDouble(POSITION_TP)))
         {
            if(EnableLogging)
               Print("Trailing SL updated: Ticket=", tradeInfo.ticket, " New SL=", newSL, " Profit=", profitPips/10, " pips");
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Update daily trade count                                         |
//+------------------------------------------------------------------+
void UpdateDailyTradeCount()
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);
   datetime currentDate = StringToTime(StringFormat("%04d.%02d.%02d", dt.year, dt.mon, dt.day));

   if(currentDate != lastTradeDate)
   {
      tradesCountToday = 0;
      lastTradeDate = currentDate;
      if(EnableLogging)
         Print("Daily trade count reset for new day: ", TimeToString(currentDate, TIME_DATE));
   }
}

//+------------------------------------------------------------------+
//| Get current spread in pips                                       |
//+------------------------------------------------------------------+
double GetCurrentSpread()
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   return (ask - bid) / _Point / 10; // Convert to pips
}

//+------------------------------------------------------------------+
//| Print trade statistics                                           |
//+------------------------------------------------------------------+
void PrintTradeStats()
{
   if(!EnableLogging)
      return;

   int totalPositions = PositionsTotal();
   double totalProfit = 0;
   int buyPositions = 0;
   int sellPositions = 0;

   //--- Count positions and calculate total profit
   for(int i = 0; i < totalPositions; i++)
   {
      if(PositionGetTicket(i) > 0)
      {
         if(PositionGetInteger(POSITION_MAGIC) == MagicNumber)
         {
            totalProfit += PositionGetDouble(POSITION_PROFIT);
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
               buyPositions++;
            else
               sellPositions++;
         }
      }
   }

   Print("=== TRADE STATISTICS ===");
   Print("Symbol: ", _Symbol);
   Print("Trades today: ", tradesCountToday, "/", MaxTradesPerDay);
   Print("Active positions: ", totalPositions, " (BUY: ", buyPositions, ", SELL: ", sellPositions, ")");
   Print("Current spread: ", DoubleToString(GetCurrentSpread(), 1), " pips");
   Print("Total floating P&L: ", DoubleToString(totalProfit, 2));
   Print("Fast EMA: ", DoubleToString(fastEMA[0], _Digits));
   Print("Slow EMA: ", DoubleToString(slowEMA[0], _Digits));
   Print("========================");
}

//+------------------------------------------------------------------+
//| Check if market is open                                          |
//+------------------------------------------------------------------+
bool IsMarketOpen()
{
   //--- Check if market is open for trading
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);

   //--- Skip weekends
   if(dt.day_of_week == 0 || dt.day_of_week == 6)
      return false;

   //--- Check if symbol is available for trading
   if(!SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE))
      return false;

   return true;
}

//+------------------------------------------------------------------+
//| OnTimer function for periodic tasks                             |
//+------------------------------------------------------------------+
void OnTimer()
{
   //--- Print statistics every hour
   static datetime lastStatsTime = 0;
   if(TimeCurrent() - lastStatsTime >= 3600) // 1 hour
   {
      PrintTradeStats();
      lastStatsTime = TimeCurrent();
   }

   //--- Reset daily trade count
   UpdateDailyTradeCount();
}

//+------------------------------------------------------------------+
//| OnTrade function - called when trade operation is completed     |
//+------------------------------------------------------------------+
void OnTrade()
{
   //--- Check for closed positions and log results
   static int lastDealsTotal = 0;
   int currentDealsTotal = HistoryDealsTotal();

   if(currentDealsTotal > lastDealsTotal)
   {
      //--- New deal detected, check if it's our trade
      if(HistorySelect(TimeCurrent() - 86400, TimeCurrent())) // Last 24 hours
      {
         for(int i = HistoryDealsTotal() - 1; i >= lastDealsTotal; i--)
         {
            ulong dealTicket = HistoryDealGetTicket(i);
            if(dealTicket > 0)
            {
               long dealMagic = HistoryDealGetInteger(dealTicket, DEAL_MAGIC);
               if(dealMagic == MagicNumber)
               {
                  //--- This is our trade
                  ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(dealTicket, DEAL_TYPE);
                  double dealProfit = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
                  double dealVolume = HistoryDealGetDouble(dealTicket, DEAL_VOLUME);
                  string dealSymbol = HistoryDealGetString(dealTicket, DEAL_SYMBOL);
                  datetime dealTime = (datetime)HistoryDealGetInteger(dealTicket, DEAL_TIME);

                  if(dealType == DEAL_TYPE_SELL || dealType == DEAL_TYPE_BUY)
                  {
                     string tradeResult = (dealProfit > 0) ? "PROFIT" : (dealProfit < 0) ? "LOSS" : "BREAKEVEN";

                     if(EnableLogging)
                     {
                        Print("TRADE CLOSED: ", tradeResult, " | Ticket: ", dealTicket,
                              " | Type: ", EnumToString(dealType), " | Symbol: ", dealSymbol,
                              " | Volume: ", dealVolume, " | Profit: ", DoubleToString(dealProfit, 2),
                              " | Time: ", TimeToString(dealTime));
                     }
                  }
               }
            }
         }
      }
      lastDealsTotal = currentDealsTotal;
   }
}
