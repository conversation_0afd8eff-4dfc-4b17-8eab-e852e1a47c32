# Scalping Expert Advisor for MetaTrader 5

## Overview
This Expert Advisor implements a scalping strategy based on engulfing candlestick patterns combined with EMA trend confirmation on the 15-minute timeframe.

## Strategy Description

### Entry Conditions
1. **Bullish Engulfing Pattern** (for BUY trades):
   - Previous candle is bearish (red)
   - Current candle is bullish (green) and completely engulfs the previous candle
   - Current candle body is larger than previous candle body

2. **Bearish Engulfing Pattern** (for SELL trades):
   - Previous candle is bullish (green)
   - Current candle is bearish (red) and completely engulfs the previous candle
   - Current candle body is larger than previous candle body

3. **EMA Trend Confirmation**:
   - **BUY**: Price above both 9 EMA and 50 EMA, and 9 EMA > 50 EMA
   - **SELL**: Price below both 9 EMA and 50 EMA, and 9 EMA < 50 EMA

### Trade Management
- **Entry**: At the close of the signal candle
- **Initial Stop Loss**: 
  - BUY: Low of signal candle
  - SELL: High of signal candle
- **Take Profit**: 1.5x the stop loss distance
- **Break Even**: Move SL to entry price when +10 pips in profit
- **Trailing Stop**: Start trailing when +15 pips in profit, trail 7 pips behind price

## Input Parameters

### Strategy Settings
- `FastEMA_Period` (9): Fast EMA period for trend confirmation
- `SlowEMA_Period` (50): Slow EMA period for trend filter
- `RiskPercent` (2.0): Risk per trade as percentage of account balance
- `TPMultiplier` (1.5): Take profit multiplier (x SL distance)

### Trailing Stop Settings
- `BreakEvenPips` (10): Move to break even after X pips profit
- `TrailStartPips` (15): Start trailing after X pips profit
- `TrailStepPips` (7): Trailing step in pips

### Risk Management
- `MaxSpread` (3.0): Maximum spread allowed in pips
- `MaxTradesPerDay` (5): Maximum number of trades per day
- `FixedLotSize` (0.0): Fixed lot size (0 = auto calculation based on risk)

### Session Filter
- `UseLondonSession` (true): Trade during London session
- `UseNewYorkSession` (true): Trade during New York session
- `LondonStartHour` (8): London session start hour (GMT)
- `LondonEndHour` (17): London session end hour (GMT)
- `NewYorkStartHour` (13): New York session start hour (GMT)
- `NewYorkEndHour` (22): New York session end hour (GMT)

### General Settings
- `MagicNumber` (123456): Unique identifier for EA trades
- `EnableLogging` (true): Enable detailed logging
- `TradeComment` ("ScalpingEA"): Comment for trades

## Installation Instructions

1. Copy `ScalpingEA.mq5` to your MetaTrader 5 `Experts` folder
2. Compile the EA in MetaEditor (F7)
3. Attach to a 15-minute chart of your preferred currency pair
4. Configure the input parameters according to your risk tolerance
5. Enable automated trading in MetaTrader 5

## Key Features

### Pattern Recognition
- Accurate bullish and bearish engulfing pattern detection
- Body size comparison for pattern validation
- Real-time pattern scanning on new bar formation

### Risk Management
- Automatic position sizing based on account balance and risk percentage
- Maximum daily trade limits
- Spread filtering to avoid high-cost trades
- Session-based trading filters

### Trade Management
- Automatic break-even adjustment
- Progressive trailing stop system
- Comprehensive trade logging and monitoring
- Real-time position tracking

### Monitoring & Logging
- Detailed trade entry and exit logs
- Hourly statistics reporting
- Trade performance tracking
- Error handling and reporting

## Recommended Settings

### Conservative Settings
- Risk Percent: 1.0%
- Max Trades Per Day: 3
- Max Spread: 2.0 pips

### Aggressive Settings
- Risk Percent: 3.0%
- Max Trades Per Day: 8
- Max Spread: 4.0 pips

## Important Notes

1. **Timeframe**: This EA is designed specifically for M15 (15-minute) charts
2. **Currency Pairs**: Works best with major pairs (EURUSD, GBPUSD, USDJPY, etc.)
3. **Market Hours**: Most effective during London and New York sessions
4. **Backtesting**: Test thoroughly on historical data before live trading
5. **VPS Recommended**: For 24/7 operation and reliable execution

## Risk Warning

Trading forex involves substantial risk of loss and is not suitable for all investors. Past performance is not indicative of future results. Always trade with money you can afford to lose and consider seeking advice from an independent financial advisor.

## Support

For questions or issues, please review the code comments and logging output. The EA provides detailed logging when `EnableLogging` is set to true.
