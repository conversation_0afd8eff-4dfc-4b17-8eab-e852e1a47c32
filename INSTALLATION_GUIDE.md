# Installation and Setup Guide for Scalping EA

## Prerequisites
- MetaTrader 5 platform installed
- Active trading account (demo or live)
- Basic understanding of MT5 Expert Advisors

## Step-by-Step Installation

### 1. File Preparation
1. Ensure you have the following files:
   - `ScalpingEA.mq5` (main Expert Advisor)
   - `TestScalpingEA.mq5` (optional test script)
   - `README.md` (documentation)

### 2. Copy Files to MetaTrader 5
1. Open MetaTrader 5
2. Press `Ctrl+Shift+D` to open the Data Folder
3. Navigate to `MQL5\Experts\`
4. Copy `ScalpingEA.mq5` to this folder
5. If using the test script, copy `TestScalpingEA.mq5` to `MQL5\Scripts\`

### 3. Compile the Expert Advisor
1. Open MetaEditor (press F4 in MT5 or click the MetaEditor icon)
2. In MetaEditor, open `ScalpingEA.mq5`
3. Press F7 or click "Compile" to compile the EA
4. Check for any compilation errors in the "Errors" tab
5. If successful, you'll see "0 error(s), 0 warning(s)" message

### 4. Attach EA to Chart
1. In MT5, open a 15-minute (M15) chart of your preferred currency pair (e.g., EURUSD)
2. In the Navigator panel, expand "Expert Advisors"
3. Find "ScalpingEA" and double-click it or drag it to the chart
4. The EA settings dialog will appear

### 5. Configure EA Settings

#### Essential Settings to Review:
- **Risk Percent**: Start with 1-2% for conservative trading
- **Max Trades Per Day**: Start with 3-5 trades
- **Max Spread**: Set to 2-3 pips for major pairs
- **Session Filters**: Enable London and/or New York sessions
- **Magic Number**: Change if running multiple EAs

#### Recommended Initial Settings:
```
=== STRATEGY SETTINGS ===
FastEMA_Period = 9
SlowEMA_Period = 50
RiskPercent = 1.5
TPMultiplier = 1.5

=== TRAILING STOP SETTINGS ===
BreakEvenPips = 10
TrailStartPips = 15
TrailStepPips = 7

=== RISK MANAGEMENT ===
MaxSpread = 2.5
MaxTradesPerDay = 3
FixedLotSize = 0.0 (auto calculation)

=== SESSION FILTER ===
UseLondonSession = true
UseNewYorkSession = true
LondonStartHour = 8
LondonEndHour = 17
NewYorkStartHour = 13
NewYorkEndHour = 22

=== GENERAL SETTINGS ===
MagicNumber = 123456
EnableLogging = true
TradeComment = "ScalpingEA"
```

### 6. Enable Automated Trading
1. Click the "AutoTrading" button in MT5 toolbar (should turn green)
2. Ensure "Allow automated trading" is checked in EA settings
3. Verify the EA is running by checking for a smiley face icon on the chart

### 7. Monitor EA Operation
1. Check the "Experts" tab for EA logs and messages
2. Monitor the "Trade" tab for open positions
3. Review the "History" tab for completed trades

## Testing Before Live Trading

### 1. Strategy Tester (Backtesting)
1. Press Ctrl+R to open Strategy Tester
2. Select "ScalpingEA" as Expert Advisor
3. Choose your symbol and M15 timeframe
4. Set date range for testing
5. Run the test and analyze results

### 2. Demo Account Testing
1. Always test on demo account first
2. Run for at least 1-2 weeks
3. Monitor performance and adjust settings
4. Verify all functions work correctly

### 3. Using Test Script (Optional)
1. In MetaEditor, open `TestScalpingEA.mq5`
2. Compile the script (F7)
3. In MT5, go to Navigator > Scripts
4. Double-click "TestScalpingEA" to run tests
5. Check results in "Experts" tab

## Troubleshooting

### Common Issues:

#### EA Not Trading
- Check if AutoTrading is enabled
- Verify spread is within MaxSpread limit
- Ensure trading session is active
- Check if daily trade limit is reached

#### Compilation Errors
- Ensure all required files are in correct folders
- Check for syntax errors in code
- Verify MT5 version compatibility

#### No Signals Generated
- Confirm chart timeframe is M15
- Check if EMA conditions are met
- Verify engulfing patterns are present
- Review session filter settings

#### Positions Not Opening
- Check account balance and margin
- Verify lot size calculations
- Ensure symbol is tradeable
- Check for trading restrictions

### Log Analysis
Enable logging and monitor these messages:
- "ScalpingEA initialized successfully" - EA started correctly
- "BUY/SELL trade opened" - Trade entry confirmation
- "Moved to Break Even" - SL moved to entry price
- "Trailing SL updated" - Trailing stop activated
- "Daily trade limit reached" - Max trades hit

## Performance Optimization

### 1. Symbol Selection
- Use major currency pairs (EURUSD, GBPUSD, USDJPY)
- Avoid exotic pairs with high spreads
- Consider volatility and liquidity

### 2. Timeframe Considerations
- EA designed specifically for M15
- Don't use on other timeframes
- Ensure sufficient historical data

### 3. Risk Management
- Start with conservative settings
- Gradually increase risk after proven performance
- Never risk more than you can afford to lose

### 4. Market Conditions
- Works best in trending markets
- May struggle in ranging/choppy conditions
- Consider market volatility when setting parameters

## Support and Maintenance

### Regular Monitoring
- Check EA performance weekly
- Review trade logs for patterns
- Adjust settings based on market conditions
- Monitor drawdown and profitability

### Updates and Modifications
- Keep backup of working configurations
- Test any changes on demo first
- Document successful parameter combinations
- Consider market condition changes

Remember: Always test thoroughly before using real money, and never risk more than you can afford to lose.
