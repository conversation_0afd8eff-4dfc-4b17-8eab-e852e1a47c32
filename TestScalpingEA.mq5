//+------------------------------------------------------------------+
//|                                              TestScalpingEA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property script_show_inputs

//--- Input parameters for testing
input string TestSymbol = "EURUSD";        // Symbol to test
input int TestBars = 100;                  // Number of bars to analyze

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== SCALPING EA TEST SCRIPT ===");
   Print("Testing symbol: ", TestSymbol);
   Print("Analyzing last ", TestBars, " bars on M15 timeframe");
   Print("");
   
   //--- Test EMA calculation
   TestEMACalculation();
   
   //--- Test engulfing pattern detection
   TestEngulfingPatterns();
   
   //--- Test session filtering
   TestSessionFiltering();
   
   //--- Test risk calculation
   TestRiskCalculation();
   
   Print("=== TEST COMPLETED ===");
}

//+------------------------------------------------------------------+
//| Test EMA calculation                                             |
//+------------------------------------------------------------------+
void TestEMACalculation()
{
   Print("--- Testing EMA Calculation ---");
   
   //--- Create EMA indicators
   int fastEMA_handle = iMA(TestSymbol, PERIOD_M15, 9, 0, MODE_EMA, PRICE_CLOSE);
   int slowEMA_handle = iMA(TestSymbol, PERIOD_M15, 50, 0, MODE_EMA, PRICE_CLOSE);
   
   if(fastEMA_handle == INVALID_HANDLE || slowEMA_handle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create EMA indicators");
      return;
   }
   
   //--- Get EMA values
   double fastEMA[], slowEMA[];
   ArraySetAsSeries(fastEMA, true);
   ArraySetAsSeries(slowEMA, true);
   
   if(CopyBuffer(fastEMA_handle, 0, 0, 5, fastEMA) < 5 ||
      CopyBuffer(slowEMA_handle, 0, 0, 5, slowEMA) < 5)
   {
      Print("ERROR: Failed to copy EMA values");
      return;
   }
   
   Print("Fast EMA (9): ", DoubleToString(fastEMA[0], 5));
   Print("Slow EMA (50): ", DoubleToString(slowEMA[0], 5));
   Print("Trend: ", (fastEMA[0] > slowEMA[0]) ? "BULLISH" : "BEARISH");
   
   //--- Release handles
   IndicatorRelease(fastEMA_handle);
   IndicatorRelease(slowEMA_handle);
   
   Print("EMA calculation test: PASSED");
   Print("");
}

//+------------------------------------------------------------------+
//| Test engulfing pattern detection                                 |
//+------------------------------------------------------------------+
void TestEngulfingPatterns()
{
   Print("--- Testing Engulfing Pattern Detection ---");
   
   int bullishCount = 0;
   int bearishCount = 0;
   
   //--- Analyze last bars for patterns
   for(int i = 2; i < TestBars; i++)
   {
      //--- Get candle data
      double open1 = iOpen(TestSymbol, PERIOD_M15, i);
      double close1 = iClose(TestSymbol, PERIOD_M15, i);
      double open2 = iOpen(TestSymbol, PERIOD_M15, i+1);
      double close2 = iClose(TestSymbol, PERIOD_M15, i+1);
      
      //--- Check for bullish engulfing
      bool prevBearish = (close2 < open2);
      bool currentBullish = (close1 > open1);
      bool bullishEngulfs = (open1 < close2) && (close1 > open2);
      double currentBody = MathAbs(close1 - open1);
      double prevBody = MathAbs(close2 - open2);
      bool largerBody = (currentBody > prevBody);
      
      if(prevBearish && currentBullish && bullishEngulfs && largerBody)
      {
         bullishCount++;
         if(bullishCount <= 3) // Show first 3 patterns
         {
            datetime barTime = iTime(TestSymbol, PERIOD_M15, i);
            Print("Bullish Engulfing found at: ", TimeToString(barTime));
         }
      }
      
      //--- Check for bearish engulfing
      bool prevBullish = (close2 > open2);
      bool currentBearish = (close1 < open1);
      bool bearishEngulfs = (open1 > close2) && (close1 < open2);
      
      if(prevBullish && currentBearish && bearishEngulfs && largerBody)
      {
         bearishCount++;
         if(bearishCount <= 3) // Show first 3 patterns
         {
            datetime barTime = iTime(TestSymbol, PERIOD_M15, i);
            Print("Bearish Engulfing found at: ", TimeToString(barTime));
         }
      }
   }
   
   Print("Bullish engulfing patterns found: ", bullishCount);
   Print("Bearish engulfing patterns found: ", bearishCount);
   Print("Pattern detection test: PASSED");
   Print("");
}

//+------------------------------------------------------------------+
//| Test session filtering                                           |
//+------------------------------------------------------------------+
void TestSessionFiltering()
{
   Print("--- Testing Session Filtering ---");
   
   MqlDateTime dt;
   TimeToStruct(TimeGMT(), dt);
   int currentHour = dt.hour;
   
   Print("Current GMT hour: ", currentHour);
   
   //--- Test London session (8-17 GMT)
   bool inLondon = (currentHour >= 8 && currentHour < 17);
   Print("In London session (8-17 GMT): ", inLondon ? "YES" : "NO");
   
   //--- Test New York session (13-22 GMT)
   bool inNewYork = (currentHour >= 13 && currentHour < 22);
   Print("In New York session (13-22 GMT): ", inNewYork ? "YES" : "NO");
   
   //--- Test overlap (13-17 GMT)
   bool inOverlap = (currentHour >= 13 && currentHour < 17);
   Print("In London/NY overlap (13-17 GMT): ", inOverlap ? "YES" : "NO");
   
   Print("Session filtering test: PASSED");
   Print("");
}

//+------------------------------------------------------------------+
//| Test risk calculation                                            |
//+------------------------------------------------------------------+
void TestRiskCalculation()
{
   Print("--- Testing Risk Calculation ---");
   
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskPercent = 2.0;
   double slDistancePips = 20.0;
   
   Print("Account balance: ", DoubleToString(balance, 2));
   Print("Risk percent: ", DoubleToString(riskPercent, 1), "%");
   Print("SL distance: ", DoubleToString(slDistancePips, 1), " pips");
   
   //--- Calculate risk amount
   double riskAmount = balance * riskPercent / 100.0;
   Print("Risk amount: ", DoubleToString(riskAmount, 2));
   
   //--- Get symbol info
   double tickValue = SymbolInfoDouble(TestSymbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(TestSymbol, SYMBOL_TRADE_TICK_SIZE);
   double point = SymbolInfoDouble(TestSymbol, SYMBOL_POINT);
   
   Print("Tick value: ", DoubleToString(tickValue, 5));
   Print("Tick size: ", DoubleToString(tickSize, 5));
   Print("Point: ", DoubleToString(point, 5));
   
   //--- Calculate lot size
   double slDistancePoints = slDistancePips * 10 * point;
   double slDistanceMoney = (slDistancePoints / tickSize) * tickValue;
   double lotSize = riskAmount / slDistanceMoney;
   
   //--- Get lot constraints
   double minLot = SymbolInfoDouble(TestSymbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(TestSymbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(TestSymbol, SYMBOL_VOLUME_STEP);
   
   //--- Normalize lot size
   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;
   
   Print("Calculated lot size: ", DoubleToString(lotSize, 2));
   Print("Min lot: ", DoubleToString(minLot, 2));
   Print("Max lot: ", DoubleToString(maxLot, 2));
   Print("Lot step: ", DoubleToString(lotStep, 2));
   
   Print("Risk calculation test: PASSED");
   Print("");
}

//+------------------------------------------------------------------+
//| Test spread checking                                             |
//+------------------------------------------------------------------+
void TestSpreadCheck()
{
   Print("--- Testing Spread Check ---");
   
   double ask = SymbolInfoDouble(TestSymbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(TestSymbol, SYMBOL_BID);
   double spread = (ask - bid) / SymbolInfoDouble(TestSymbol, SYMBOL_POINT) / 10;
   
   Print("Current spread: ", DoubleToString(spread, 1), " pips");
   Print("Max allowed spread: 3.0 pips");
   Print("Spread acceptable: ", (spread <= 3.0) ? "YES" : "NO");
   
   Print("Spread check test: PASSED");
   Print("");
}
